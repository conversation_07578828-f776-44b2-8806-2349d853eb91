import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/my_team_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/my_team_entity.g.dart';

@JsonSerializable()
class MyTeamEntity {
  int? teamSize;
  double? teamBetAmount;
  double? teamCashinAmount;

  MyTeamEntity();

  factory MyTeamEntity.fromJson(Map<String, dynamic> json) =>
      $MyTeamEntityFromJson(json);

  Map<String, dynamic> toJson() => $MyTeamEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
