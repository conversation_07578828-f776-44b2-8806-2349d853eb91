import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:wd/core/base/base_stateful_page.dart';
import 'package:wd/core/constants/assets.dart';
import 'package:wd/core/constants/enums.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/singletons/user_singleton.dart';
import 'package:wd/core/singletons/user_state.dart';
import 'package:wd/core/utils/clipboardTool.dart';
import 'package:wd/core/utils/global_config.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/core/utils/system_util.dart';
import 'package:wd/features/page/4_mine/account_security/modify_pwd/modify_pwd_view.dart';
import 'package:wd/features/page/4_mine/account_security/payment_list/payment_list_state.dart';
import 'package:wd/features/routers/app_router.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/common_dialog.dart';
import 'package:wd/shared/widgets/common_switch.dart';

import 'account_security_cubit.dart';
import 'account_security_state.dart';

/// 安全中心
class AccountSecurityPage extends BasePage {
  const AccountSecurityPage({super.key});

  @override
  BasePageState<BasePage> getState() => _AccountSecurityPageState();
}

class _AccountSecurityPageState extends BasePageState<AccountSecurityPage> {
  @override
  void initState() {
    pageTitle = "security".tr();
    super.initState();
  }

  Widget _cellItem(
    BuildContext context,
    String title, {
    Widget? leadWidget,
    String? subTitle,
    Widget? tailWidget,
    bool showNextIcon = false,
    bool isLast = false,
    VoidCallback? callback,
  }) {
    return Column(
      children: [
        GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: callback,
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.3), // 阴影颜色
                  spreadRadius: 2, // 阴影扩散半径
                  blurRadius: 10, // 阴影模糊半径
                ),
              ],
              borderRadius: BorderRadius.circular(10),
            ),
            height: 60.gw,
            padding: EdgeInsets.symmetric(horizontal: 12.gw),
            child: Row(
              children: [
                if (leadWidget != null) leadWidget,
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w500),
                    ),
                    SizedBox(height: 2.gw),
                    Text(
                      subTitle ?? "",
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ],
                ),
                const Spacer(),
                tailWidget ?? Container(),
                Offstage(
                  offstage: !showNextIcon,
                  child: Image.asset(
                    Assets.nextIcon,
                    width: 15.gw,
                    height: 15.gw,
                  ),
                ),
              ],
            ),
          ),
        ),
        if (!isLast) SizedBox(height: 10.gw),
      ],
    );
  }

  void _navigateToUpdateProfileScreen(BuildContext context, String initialValue, UserFieldType field) async {
    final updatedValue = await sl<NavigatorService>().push(
      AppRouter.updateProfile,
      arguments: {
        'initialValue': initialValue,
        'field': field,
      },
    );

    if (updatedValue != null && context.mounted) {
      // Update the AccountSecurityCubit state with the new value
      BlocProvider.of<AccountSecurityCubit>(context).updateField(field, updatedValue);
    }
  }

  Widget mainPageWidget(AccountSecurityState state) {
    // String phoneNo = state.userInfo?.phoneNo.isNotEmpty ?? false ? state.userInfo!.phoneNo : state.phone ?? "";
    // String email = state.userInfo?.email.isNotEmpty ?? false ? state.userInfo!.email : state.email ?? "";

    return SingleChildScrollView(
      padding: EdgeInsets.symmetric(horizontal: 12.5.gw, vertical: 10.gw),
      child: AnimationLimiter(
          child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: AnimationConfiguration.toStaggeredList(
          duration: const Duration(milliseconds: 375),
          childAnimationBuilder: (widget) => SlideAnimation(
            horizontalOffset: 50.0,
            child: FadeInAnimation(
              child: widget,
            ),
          ),
          children: [
            _buildTitle('basic_information'.tr()),
            _cellItem(
              context,
              "real_name".tr(),
              subTitle: "real_name_tips".tr(),
              leadWidget: buildImageWidget(Assets.iconProfile),
              tailWidget: Text(
                sl<UserCubit>().state.userInfo?.realName ?? "unset".tr(),
                style: Theme.of(context).textTheme.titleMedium,
              ),
              showNextIcon: (sl<UserCubit>().state.userInfo?.realName ?? "").isEmpty,
              callback: () {
                if ((sl<UserCubit>().state.userInfo?.realName ?? "").isEmpty) {
                  sl<NavigatorService>().push(
                    AppRouter.updateProfile,
                    arguments: {
                      'initialValue': '',
                      'field': UserFieldType.realName,
                    },
                  );
                  // GSEasyLoading.showToast("请通过添加提现银行卡设置");
                }
              },
            ),
            BlocSelector<UserCubit, UserState, String>(
              selector: (state) => state.userInfo?.phoneNo ?? '',
              builder: (context, phoneNo) {
                return _cellItem(
                  context,
                  "phone".tr(),
                  subTitle: phoneNo.isEmpty ? "phone_no_tips".tr() : phoneNo.toMaskedFormat(),
                  leadWidget: buildImageWidget(Assets.iconPhone),
                  tailWidget: phoneNo.isEmpty
                      ? Text('unset'.tr(),
                          style: Theme.of(context).textTheme.bodyMedium!.copyWith(color: Colors.red[300]))
                      : Text('set'.tr(),
                          style: Theme.of(context).textTheme.bodyMedium!.copyWith(color: Colors.black38)),
                  showNextIcon: phoneNo.isEmpty,
                  callback: () {
                    _navigateToUpdateProfileScreen(context, phoneNo, UserFieldType.phone);
                  },
                );
              },
            ),
            BlocSelector<UserCubit, UserState, String>(
              selector: (state) => state.userInfo?.email ?? '',
              builder: (context, email) {
                return _cellItem(
                  context,
                  "email".tr(),
                  subTitle: email.isEmpty ? "email_tips".tr() : email,
                  leadWidget: buildImageWidget(Assets.iconEmail),
                  tailWidget: email.isEmpty
                      ? Text('unset'.tr(),
                          style: Theme.of(context).textTheme.bodyMedium!.copyWith(color: Colors.red[300]))
                      : Text('set'.tr(),
                          style: Theme.of(context).textTheme.bodyMedium!.copyWith(color: Colors.black38)),
                  showNextIcon: email.isEmpty,
                  isLast: true,
                  callback: () {
                    _navigateToUpdateProfileScreen(context, email, UserFieldType.email);
                  },
                );
              },
            ),
            _buildTitle('提现'.tr()),
            _cellItem(context, "提现钱包",
                subTitle: "提现钱包管理".tr(),
                leadWidget: buildImageWidget(Assets.iconCreditCard),
                showNextIcon: true, callback: () {
              if (sl<UserCubit>().state.userInfo!.hasFundPwd) {
                sl<NavigatorService>().push(AppRouter.userWithdrawList, arguments: PaymentType.wallet);
              } else {
                SystemUtil.showSetFundPwdDialog(context);
              }
            }),
            _cellItem(context, "提现银行卡".tr(),
                subTitle: "提现银行卡管理".tr(),
                leadWidget: buildImageWidget(Assets.iconWallet),
                showNextIcon: true, callback: () {
              if (sl<UserCubit>().state.userInfo!.hasFundPwd) {
                sl<NavigatorService>().push(AppRouter.userWithdrawList, arguments: PaymentType.bankCard);
              } else {
                SystemUtil.showSetFundPwdDialog(context);
              }
            }),
            if (GlobalConfig.needShowManualUSDTWithdrawWidget())
              _cellItem(context, "USDT地址".tr(),
                  subTitle: "提现USDT地址管理".tr(),
                  leadWidget: buildImageWidget(Assets.iconUSD),
                  showNextIcon: true, callback: () {
                if (sl<UserCubit>().state.userInfo!.hasFundPwd) {
                  sl<NavigatorService>().push(AppRouter.userWithdrawList, arguments: PaymentType.usdt);
                } else {
                  SystemUtil.showSetFundPwdDialog(context);
                }
              }),
            _buildTitle('密码设置'.tr()),
            _cellItem(context, "modify_login_password".tr(),
                subTitle: "fund_password_tips".tr(),
                showNextIcon: true,
                leadWidget: buildImageWidget(Assets.iconPassword),
                callback: () =>
                    sl<NavigatorService>().push(AppRouter.userModifyPwd, arguments: SetPasswordType.modifyLoginPwd)),
            _cellItem(context, "fund_password".tr(),
                subTitle: "fund_password_tips".tr(),
                leadWidget: buildImageWidget(Assets.iconPassword),
                showNextIcon: true, callback: () {
              final type = sl<UserCubit>().state.userInfo!.hasFundPwd
                  ? SetPasswordType.modifyFundPwd
                  : SetPasswordType.setFundPwd;
              sl<NavigatorService>().push(AppRouter.userModifyPwd, arguments: type);
            }),
            _buildTitle('其他'.tr()),
            _cellItem(
              context,
              sl<UserCubit>().state.userInfo?.userNo ?? "username".tr(),
              subTitle: "my_account".tr(),
              leadWidget: buildImageWidget(Assets.iconIdentity),
              tailWidget: GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () => ClipboardTool.setDataToast(sl<UserCubit>().state.userInfo?.userNo ?? "",
                      msg: "copied_to_clipboard".tr()),
                  child: Container(
                    width: 50.gw,
                    height: 40.gw,
                    alignment: Alignment.centerRight,
                    child: Text("copy".tr(),
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: Colors.blue[800])),
                  )),
            ),
            _cellItem(
              context,
              "视频".tr(),
              subTitle: "视频界面显示与隐藏".tr(),
              leadWidget: buildImageWidget(Assets.iconVideo),
              tailWidget: BlocBuilder<UserCubit, UserState>(
                // buildWhen: (previous, current) =>
                //     current.userInfo?.tiktokTabVisible != previous.userInfo?.tiktokTabVisible,
                builder: (context, state) {
                  print(">>>state..userInfo?.tiktokTabVisibl >> ${state.userInfo?.tiktokTabVisible}");
                  return CommonSwitch(
                    value: state.userInfo?.tiktokTabVisible ?? true,
                    onChanged: (bool value) => context.read<AccountSecurityCubit>().updateMovieTabVisible(value),
                  );
                },
              ),
            ),
          ],
        ),
      )),
    );
  }

  _buildTitle(text) {
    return Container(
      height: 45.gw,
      alignment: Alignment.centerLeft,
      child: Text(text, style: Theme.of(context).textTheme.titleMedium),
    );
  }

  buildImageWidget(String imageUrl) =>
      Padding(padding: EdgeInsets.only(right: 8.gw), child: Image.asset(imageUrl, width: 24.gw, height: 24.gw));

  @override
  Widget buildPage(BuildContext context) {
    return BlocBuilder<AccountSecurityCubit, AccountSecurityState>(
      builder: (context, state) {
        return mainPageWidget(state);
      },
    );
  }
}
