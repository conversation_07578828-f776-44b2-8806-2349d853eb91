import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:flutter_svg/svg.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/base/base_stateful_page.dart';
import 'package:wd/core/base/common_refresher.dart';
import 'package:wd/core/constants/assets.dart';
import 'package:wd/core/constants/enums.dart';
import 'package:wd/core/models/apis/transact.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/features/page/4_mine/statement/statement_cubit.dart';
import 'package:wd/features/page/4_mine/statement/statement_state.dart';
import 'package:wd/shared/widgets/order/GSDateTabBarWidget.dart';
import 'package:wd/shared/widgets/statement/statement_cell.dart';
import 'package:wd/shared/widgets/transact/transact_history_filter_popup.dart';

class StatementPage extends BasePage {
  const StatementPage({super.key});

  @override
  BasePageState<BasePage> getState() => _StatementPageState();
}

class _StatementPageState extends BasePageState<StatementPage> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  late Color cardBgColor;

  /// 刷新组件控制器
  final RefreshController refreshController = RefreshController(initialRefresh: false);

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {
          cardBgColor = Theme.of(context).scaffoldBackgroundColor;
        });
      }
    });
    pageTitle = 'personal_account_change'.tr();

    isNeedEmptyDataWidget = false;
    isAllowBack = false;
    _tabController = TabController(length: 3, vsync: this);
    _tabController.addListener(_handleTabChange);
  }

  void _handleTabChange() {
    if (_tabController.indexIsChanging) {
      context.read<StatementCubit>().currentDateTypeChanged(
            RecordDateType.values[_tabController.index],
          );
    }
  }

  _getData() {
    BlocProvider.of<StatementCubit>(context).fetchListData();
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleTabChange);
    _tabController.dispose();
    refreshController.dispose();
    super.dispose();
  }

  @override
  Widget right() {
    return GestureDetector(
      onTap: () {
        final cubit = context.read<StatementCubit>();
        final state = cubit.state;
        TransactHistoryFilterPopup.show(
          context: context,
          type: TransactType.statement,
          filterWayList: state.filterWayList,
          filterTypeList: state.filterTypeList,
          onClickSure: () {
            cubit.updatePageNo(1);
            cubit.fetchListDataWithFilter();
          },
        );
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
        decoration: BoxDecoration(
          color: context.colorTheme.foregroundColor,
          borderRadius: BorderRadius.circular(8),
        ),
        child: SvgPicture.asset(
          Assets.filterIcon,
          fit: BoxFit.contain,
        ),
      ),
    );
  }
 

  /// 下拉刷新
  void _onRefresh() {
    _getData();
  }

  Widget mainPageWidget(StatementState state) {
    return GSDateTabBarWidget(tabController: _tabController, children: [
      if (state.netState == NetState.emptyDataState) ...[
        Expanded(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              emptyWidget('empty_popular'.tr()),
            ],
          ),
        )
      ],
      if (state.netState == NetState.dataSuccessState) ...[
        Expanded(
          child: AnimationLimiter(
            child: CommonRefresher(
              bgColor: cardBgColor,
              enablePullDown: true,
              enablePullUp: true,
              refreshController: refreshController,
              onRefresh: _onRefresh,
              onLoading: _onLoading,
              listWidget: ListView.separated(
                itemBuilder: (context, index) => AnimationConfiguration.staggeredList(
                  position: index,
                  duration: const Duration(milliseconds: 375),
                  child: SlideAnimation(
                    horizontalOffset: 50.0,
                    child: FadeInAnimation(
                      child: GSStateListCell(model: state.dataList![index]),
                    ),
                  ),
                ),
                separatorBuilder: (_, __) => SizedBox(height: 10.gw),
                itemCount: state.dataList!.length,
              ),
            ),
          ),
        )
      ],
    ]);
  }

  void _listener(BuildContext context, StatementState state) {
    refreshController.refreshCompleted();
    refreshController.loadComplete();
    if (state.isNoMoreDataState == true) {
      refreshController.loadNoData();
    }
  }

  void _onLoading() {
    context.read<StatementCubit>().updatePageNoToNext();
    context.read<StatementCubit>().fetchListData();
  }

  @override
  Widget buildPage(BuildContext context) {
    return BlocConsumer<StatementCubit, StatementState>(
      listener: _listener,
      builder: (context, state) {
        return resultWidget(state, (baseState, context) => mainPageWidget(state), refreshMethod: () {
          _getData();
        });
      },
    );
  }
}
