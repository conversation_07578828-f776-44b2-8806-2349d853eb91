import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/constants/assets.dart';
import 'package:wd/core/constants/enums.dart';
import 'package:wd/core/models/entities/order_main_entity.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/shared/widgets/common_text_colum.dart';
import 'package:wd/shared/widgets/header_content_card.dart';

class GSOrderListViewModel {
  String title;
  String id;
  String logo;
  String? gameClassCode;
  String? categoryCode;
  String returnAmount; // 游戏返水金额
  double betAmount; // 下单金额
  double winAmount; // 盈亏金额

  GSOrderListViewModel({
    required this.title,
    required this.id,
    required this.logo,
    this.gameClassCode,
    this.categoryCode,
    required this.returnAmount,
    required this.betAmount,
    required this.winAmount,
  });

  factory GSOrderListViewModel.formOrderMainEntity(OrderMainEntity model) {
    return GSOrderListViewModel(
        title: model.gameClassName,
        id: model.gameClassCode,
        logo: ChannelType.getLogoByCode(model.gameClassCode),
        returnAmount: model.rebate.formattedMoney,
        betAmount: model.totalBetAmount,
        winAmount: model.totalWin);
  }

  factory GSOrderListViewModel.formOrderPlatformEntity(OrderPlatformEntity model) {
    return GSOrderListViewModel(
        title: model.platFormName,
        id: model.platFormId.toString(),
        gameClassCode: model.gameClassCode,
        categoryCode: model.categoryCode,
        logo: ChannelType.getLogoByCode(model.gameClassCode),
        returnAmount: model.rebate.formattedMoney,
        betAmount: model.totalBetAmount,
        winAmount: model.totalWin);
  }
}

class GSOrderMainListCell extends StatelessWidget {
  final VoidCallback? onPressed;
  final GSOrderListViewModel model;
  final bool showTitle;

  const GSOrderMainListCell({super.key, required this.model, this.onPressed, this.showTitle = true});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Theme.of(context).scaffoldBackgroundColor,
      margin: EdgeInsets.symmetric(horizontal: 12.gw),
      height: 250.gw,
      child: Column(children: [
        HeaderContentCard(
          header: Row(
            children: [
              Text(model.title),
              const Spacer(),
              CommonButton(
                title: 'more'.tr(),
                onPressed: onPressed,
                width: 70,
                height: 30,
                borderColor: Colors.transparent,
                style: CommonButtonStyle.quaternary,
              ),
            ],
          ),
          content: Text(model.id),
        ),
        SizedBox(height: 12.gw),
        _getTopWidget(context),
        Divider(height: 1, color: Theme.of(context).dividerColor),
        Expanded(child: _getBottomWidget(context, showTitle: showTitle)),
      ]),
    );
  }

  _getTopWidget(context) {
    return Container(
      height: 50.gw,
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle, // 可以根据需要设置形状
                    boxShadow: [
                      BoxShadow(
                        color: ChannelType.getShadowColorByCode(model.id).withOpacity(0.4),
                        blurRadius: 20,
                        offset: const Offset(0, 0),
                      ),
                    ],
                  ),
                  child: Image.asset(model.logo, width: 35, height: 35)),
              SizedBox(width: 3.gw),
              Text(
                model.title,
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.w600),
              )
            ],
          ),
          GestureDetector(
            onTap: onPressed,
            behavior: HitTestBehavior.opaque,
            child: SizedBox(
              height: 40.gw,
              child: Row(
                children: [
                  Text('   更多', style: Theme.of(context).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w300)),
                  Image.asset(
                    Assets.nextIcon,
                    width: 15,
                    height: 20.gw,
                  )
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  _getBottomWidget(context, {bool showTitle = true}) {
    return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: showTitle
            ? Row(
                // mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Expanded(
                    flex: 1,
                    child: CommonTextColumn(
                      topText: '游戏返水',
                      bottomText: model.returnAmount,
                      topTextStyle: Theme.of(context).textTheme.bodyLarge,
                      bottomTextStyle: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontFamily: 'WeChatSansStd',
                            fontWeight: FontWeight.w400,
                          ),
                      spacing: 8.gw,
                      crossAxisAlignment: CrossAxisAlignment.start,
                    ),
                  ),
                  Expanded(
                    flex: 1,
                    child: CommonTextColumn(
                      topText: '下单金额',
                      bottomText: "¥${model.betAmount.formattedMoney}",
                      topTextStyle: Theme.of(context).textTheme.bodyLarge,
                      bottomTextStyle: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontFamily: 'WeChatSansStd',
                            fontWeight: FontWeight.w400,
                          ),
                      spacing: 8.gw,
                      crossAxisAlignment: CrossAxisAlignment.center,
                    ),
                  ),
                  Expanded(
                    flex: 1,
                    child: CommonTextColumn(
                      topText: '盈亏总额',
                      bottomText: '¥${model.winAmount.formattedMoney}',
                      topTextStyle: Theme.of(context).textTheme.bodyLarge,
                      bottomTextStyle: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontFamily: 'WeChatSansStd',
                            fontWeight: FontWeight.w400,
                            color: model.winAmount >= 0 ? const Color(0xff67ac5c) : const Color(0xffe23f3b),
                          ),
                      spacing: 8.gw,
                      crossAxisAlignment: CrossAxisAlignment.end,
                    ),
                  ),
                ],
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CommonTextColumn(
                    topText: '下单金额',
                    bottomText: "¥${model.betAmount.formattedMoney}",
                    topTextStyle: Theme.of(context).textTheme.bodyLarge,
                    bottomTextStyle: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontFamily: 'WeChatSansStd',
                          fontWeight: FontWeight.w400,
                        ),
                    spacing: 8.gw,
                    crossAxisAlignment: CrossAxisAlignment.center,
                  ),
                  CommonTextColumn(
                    topText: '盈亏总额',
                    bottomText: '¥${model.winAmount.formattedMoney}',
                    topTextStyle: Theme.of(context).textTheme.bodyLarge,
                    bottomTextStyle: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontFamily: 'WeChatSansStd',
                          fontWeight: FontWeight.w400,
                          color: model.winAmount >= 0 ? const Color(0xff67ac5c) : const Color(0xffe23f3b),
                        ),
                    spacing: 8.gw,
                    crossAxisAlignment: CrossAxisAlignment.end,
                  ),
                ],
              ));
  }
}
