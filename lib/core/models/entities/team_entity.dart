import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/team_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/team_entity.g.dart';

@JsonSerializable()
class TeamEntity {
  int? userTeamLevel;
  List<TeamTeamConfigList>? teamConfigList;

  TeamEntity();

  factory TeamEntity.fromJson(Map<String, dynamic> json) =>
      $TeamEntityFromJson(json);

  Map<String, dynamic> toJson() => $TeamEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class TeamTeamConfigList {
  int? teamLevel;
  double? betRebate;
  double? cashinRebate;
  double? maxAmount;

  TeamTeamConfigList();

  factory TeamTeamConfigList.fromJson(Map<String, dynamic> json) =>
      $TeamTeamConfigListFromJson(json);

  Map<String, dynamic> toJson() => $TeamTeamConfigListToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
