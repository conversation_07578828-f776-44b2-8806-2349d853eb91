import 'package:flutter/material.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';

class CommonCard extends StatelessWidget {
  const CommonCard({super.key, required this.child, this.color, this.radius, this.padding, this.margin});

  final Widget child;
  final Color? color;
  final double? radius;
  final double? padding;
  final double? margin;
  @override
  Widget build(BuildContext context) {
    return Card(
      margin: EdgeInsets.all(margin ?? 0.gw),
      color: color ?? context.theme.cardColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(radius ?? 6),
      ),
      child: Padding(
        padding: EdgeInsets.all(padding ?? 16.gw),
        child: child,
      ),
    );
  }
}
