import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wd/core/base/base_stateful_page.dart';
import 'package:wd/core/constants/enums.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/singletons/user_state.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/auth_util.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/features/page/4_mine/profile/profile_cubit.dart';
import 'package:wd/features/page/4_mine/profile/profile_state.dart';
import 'package:wd/features/routers/app_router.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/common_card_page.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

import '../../../../core/constants/assets.dart';
import 'widgets/profile_account_trailing.dart';
import 'widgets/profile_avatar_picker.dart';
import 'widgets/profile_date_picker.dart';
import 'widgets/profile_field.dart';
import 'widgets/profile_gender_dropdown.dart';
import 'widgets/profile_section.dart';

class UserProfilePage extends BasePage {
  const UserProfilePage({super.key});

  @override
  BasePageState<BasePage> getState() => _UserProfilePageState();
}

class _UserProfilePageState extends BasePageState {
  @override
  void initState() {
    pageTitle = 'personal_information'.tr();
    super.initState();
  }

  @override
  Widget buildPage(BuildContext context) {
    return BlocBuilder<UserProfileCubit, UserProfileState>(
      builder: (context, userInfoState) {
        return CommonCardPage(
          child: SingleChildScrollView(
            primary: true,
            child: Column(
              children: [
                SizedBox(height: 20.gw),
                // Profile Picture Section
                _buildProfilePictureSection(context),
                SizedBox(height: 20.gw),
                // Account Details Section
                _buildAccountDetailsSection(context, userInfoState),
                SizedBox(height: 20.gw),
                // Contact Information Section
                _buildContactInformationSection(context, userInfoState),
                SizedBox(height: 20.gw),
                // Personal Information Section
                _buildPersonalInformationSection(context, userInfoState),
                SizedBox(height: 20.gw),
              ],
            ),
          ),
        );
      },
    );
  }

  // Profile Picture Section
  Widget _buildProfilePictureSection(BuildContext context) {
    return BlocBuilder<UserCubit, UserState>(
      builder: (context, state) {
        return GestureDetector(
          onTap: () => ProfileAvatarPicker.show(context),
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 16.gw),
            margin: EdgeInsets.symmetric(horizontal: 20.gw),
            decoration: BoxDecoration(
              color: context.theme.cardColor,
              borderRadius: BorderRadius.circular(12.gw),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    AuthUtil.getAvatarWidget(
                      context,
                      avatarStr: state.userInfo?.faceId.toString() ?? '',
                      size: Size(56.gw, 60.gw),
                    ),
                    SizedBox(width: 16.gw),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        AneText(state.userInfo?.nickName ?? 'not_set'.tr(),
                            style: context.textTheme.secondary.fs20.w500),
                        AneText(state.userInfo?.userNo ?? 'not_set'.tr(),
                            style: context.textTheme.title.fs16),
                      ],
                    ),
                  ],
                ),
                Image.asset(Assets.forwardIcon, width: 32.gw, height: 32.gw),
              ],
            ),
          ),
        );
      },
    );
  }

  // Account Details Section
  Widget _buildAccountDetailsSection(
      BuildContext context, UserProfileState state) {
    return ProfileSection(
      title: 'account_details'.tr(),
      child: Column(
        children: [
          ProfileField(
            icon: Assets.iconProfileUser,
            title: 'account'.tr(),
            trailing: ProfileAccountTrailing(account: state.account ?? ''),
            onTap: null,
          ),
          const ProfileFieldDivider(),
          ProfileField(
            icon: Assets.iconProfileNickname,
            title: 'nickName'.tr(),
            trailing: AneText(
              state.nickName ?? 'not_set'.tr(),
              style: context.textTheme.title.fs13,
            ),
            onTap: () => _navigateToTextInputScreen(
                context, state.nickName ?? '', UserFieldType.nickName),
          ),
        ],
      ),
    );
  }

  // Contact Information Section
  Widget _buildContactInformationSection(
      BuildContext context, UserProfileState state) {
    return ProfileSection(
      title: 'contact_information'.tr(),
      child: Column(
        children: [
          ProfileField(
            icon: Assets.iconProfilePhone,
            title: 'phone_number'.tr(),
            trailing: AneText(
              state.phone ?? 'not_bound'.tr(),
              style: context.textTheme.title.fs13,
            ),
            onTap: () => _navigateToTextInputScreen(
                context, state.phone ?? '', UserFieldType.phone),
          ),
          const ProfileFieldDivider(),
          ProfileField(
            icon: Assets.iconAppBarNotice,
            title: 'email'.tr(),
            trailing: AneText(
              state.email ?? 'not_bound'.tr(),
              style: context.textTheme.title.fs13,
            ),
            onTap: () => _navigateToTextInputScreen(
                context, state.email ?? '', UserFieldType.email),
          ),
        ],
      ),
    );
  }

  // Personal Information Section
  Widget _buildPersonalInformationSection(
      BuildContext context, UserProfileState state) {
    return ProfileSection(
      title: 'personal_information'.tr(),
      child: Column(
        children: [
          ProfileField(
            icon: Assets.iconProfileUser,
            title: 'gender'.tr(),
            trailing: ProfileGenderDropdown(state: state),
            onTap: null,
          ),
          const ProfileFieldDivider(),
          ProfileField(
            icon: Assets.iconProfileBirthday,
            title: 'birthday'.tr(),
            trailing: AneText(
              state.birthday?.formattedDate ?? 'not_set'.tr(),
              style: context.textTheme.title.fs13,
            ),
            onTap: () => ProfileDatePicker.show(context),
          ),
        ],
      ),
    );
  }

  void _navigateToTextInputScreen(
      BuildContext context, String initialValue, UserFieldType field) async {
    final updatedValue = await sl<NavigatorService>().push(
      AppRouter.updateProfile,
      arguments: {
        'initialValue': initialValue,
        'field': field,
      },
    );

    if (updatedValue != null && mounted && context.mounted) {
      // Update the UserProfileCubit state with the new value
      context.read<UserProfileCubit>().updateField(field, updatedValue);
    }
  }
}
