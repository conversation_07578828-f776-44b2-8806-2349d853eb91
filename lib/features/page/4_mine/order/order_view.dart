import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/base/base_stateful_page.dart';
import 'package:wd/core/base/common_refresher.dart';
import 'package:wd/core/constants/enums.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/features/routers/app_router.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/common_card.dart';
import 'package:wd/shared/widgets/order/GSDateTabBarWidget.dart';
import 'package:wd/shared/widgets/order/order_main_cell.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

import 'order_cubit.dart';
import 'order_state.dart';

class OrderPage extends BasePage {
  const OrderPage({super.key});

  @override
  BasePageState<BasePage> getState() => _OrderPageState();
}

class _OrderPageState extends BasePageState<OrderPage> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  /// 刷新组件控制器
  final RefreshController refreshController = RefreshController(initialRefresh: false);

  @override
  void initState() {
    super.initState();
    pageTitle = "orders".tr();
    _tabController = TabController(length: 3, vsync: this)
      ..addListener(() {
        BlocProvider.of<OrderCubit>(context).currentDateTypeChanged(RecordDateType.values[_tabController.index]);
      });
    _getData();
  }

  _getData() {
    BlocProvider.of<OrderCubit>(context).fetchListData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    refreshController.dispose();
    super.dispose();
  }

  /// 下拉刷新
  void _onRefresh() {
    _getData();
  }

  Widget mainPageWidget(OrderState state) {
    return GSDateTabBarWidget(
      tabController: _tabController,
      children: [
        if (state.netState == NetState.dataSuccessState) ...[
          CommonCard(
            margin: 12.gw,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                AneText('total_profit_and_loss'.tr(), style: context.textTheme.title.fs16),
                AneText(
                  state.totalWinAmount.formattedMoney,
                  style: context.textTheme.primary.fs20,
                )
              ],
            ),
          ),
          Expanded(
            child: AnimationLimiter(
              child: CommonRefresher(
                enablePullDown: true,
                enablePullUp: false,
                refreshController: refreshController,
                onRefresh: _onRefresh,
                onLoading: () {},
                listWidget: ListView.separated(
                  padding: EdgeInsets.only(bottom: MediaQuery.of(context).padding.bottom),
                  itemBuilder: (context, index) {
                    final model = state.dataList![index];
                    return AnimationConfiguration.staggeredList(
                      position: index,
                      duration: const Duration(milliseconds: 375),
                      child: SlideAnimation(
                        horizontalOffset: 50.0,
                        child: FadeInAnimation(
                          child: GSOrderMainListCell(
                            model: model,
                            onPressed: () => sl<NavigatorService>().push(AppRouter.orderChannelRecordList, arguments: {
                              "gameClassCode": model.id,
                              "gameClassName": model.title,
                              "dateType": RecordDateType.values[_tabController.index],
                              "returnAmount": model.returnAmount,
                            }),
                          ),
                        ),
                      ),
                    );
                  },
                  separatorBuilder: (_, __) => SizedBox(height: 10.gw),
                  itemCount: state.dataList!.length,
                ),
              ),
            ),
          ),
        ],
      ],
    );
  }

  void _listener(BuildContext context, OrderState state) {
    refreshController.refreshCompleted();
    refreshController.loadComplete();
    if (state.isNoMoreDataState == true) {
      refreshController.loadNoData();
    }
  }

  @override
  Widget buildPage(BuildContext context) {
    return BlocConsumer<OrderCubit, OrderState>(
      listener: _listener,
      builder: (context, state) {
        return resultWidget(state, (baseState, context) => mainPageWidget(state), refreshMethod: () {
          _getData();
        });
      },
    );
  }
}
