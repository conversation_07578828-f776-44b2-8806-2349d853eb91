import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/constants/enums.dart';
import 'package:wd/shared/widgets/transact/transact_history_filter_popup.dart';

class StatementState extends BaseState {
  List<TransactFilterItem> filterWayList = [];
  List<TransactFilterItem> filterTypeList = [];
  int pageNo = 1;
  RecordDateType currentDateType = RecordDateType.today;

  StatementState init() {
    return StatementState()
      ..pageNo = 1
      ..filterWayList = []
      ..filterTypeList = []
      ..dataList = []
      ..isNoMoreDataState = false
      ..currentDateType = RecordDateType.today;
  }

  StatementState clone() {
    return StatementState()
      ..pageNo = pageNo
      ..filterWayList = filterWayList
      ..filterTypeList = filterTypeList
      ..dataList = dataList
      ..currentDateType = currentDateType
      ..isNoMoreDataState = isNoMoreDataState
      ..netState = netState;
  }
}//
