import 'package:bloc/bloc.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/constants/enums.dart';
import 'package:wd/core/models/apis/transact.dart';
import 'package:wd/core/utils/time_util.dart';
import 'package:wd/shared/widgets/easy_loading.dart';
import 'package:wd/shared/widgets/transact/transact_history_filter_popup.dart';

import 'top_up_history_list_state.dart';

class TopUpHistoryListCubit extends Cubit<TopUpHistoryListState> {
  TopUpHistoryListCubit() : super(TopUpHistoryListState().init()) {
    fetchListFilterWayList();
    fetchListFilterTypeList();
    fetchListData();
  }

  String lastWayCode = "";
  String lastTypeCode = "";

  void currentDateTypeChanged(RecordDateType type) {
    state.currentDateType = type;
    fetchListData();
  }

  void updatePageNo(int pageNo) {
    if (isClosed) return;
    state.pageNo = pageNo;
    emit(state.clone());
  }

  void updatePageNoToNext() {
    if (isClosed) return;
    state.pageNo += 1;
    emit(state.clone());
  }

  void updateIsNoMoreDataState(bool isNoMoreDataState) {
    state.isNoMoreDataState = isNoMoreDataState;
    emit(state.clone());
  }

  fetchListFilterWayList() async {
    var list = await TransactApi.fetchTransactFilterWay(type: TransactType.withdraw);
    var tmpList = list.map((e) => TransactFilterItem.fromTransactFilterWay(e)).toList();
    if (tmpList.isNotEmpty) {
      var allType = TransactFilterItem(name: "全部", code: "", isSel: true);
      state.filterWayList = List.from(tmpList)..insert(0, allType);
      emit(state.clone());
    }
  }

  fetchListFilterTypeList() async {
    var list = await TransactApi.fetchTransactFilterType(type: TransactType.withdraw);
    var tmpList = list.map((e) => TransactFilterItem.fromTransactFilterType(e)).toList();
    if (tmpList.isNotEmpty) {
      var allType = TransactFilterItem(name: "全部", code: "", isSel: true);
      state.filterTypeList = List.from(tmpList)..insert(0, allType);
      emit(state.clone());
    }
  }

  fetchListDataWithFilter() {
    if (isClosed) return;
    final wayCode = state.filterWayList.where((e) => e.isSel).map((e) => e.code).toList().join(",");
    final typeCode = state.filterTypeList.where((e) => e.isSel).map((e) => e.code).toList().join(",");
    if (wayCode != lastWayCode || typeCode != lastTypeCode) {
      lastWayCode = wayCode;
      lastTypeCode = typeCode;
      fetchListData();
    }
  }

  fetchListData() async {
    GSEasyLoading.showLoading();
    final dateRange = TimeUtil.getDateRange(state.currentDateType);
    final wayCode = state.filterWayList.where((e) => e.isSel).map((e) => e.code).toList().join(",");
    final typeCode = state.filterTypeList.where((e) => e.isSel).map((e) => e.code).toList().join(",");
    final result = await TransactApi.fetchTopUpHistoryList(
      pageNo: state.pageNo,
      startDate: dateRange.$1,
      endDate: dateRange.$2,
      wayCode: wayCode,
      typeCode: typeCode,
    );
    GSEasyLoading.dismiss();
    if (result != null) {
      if (result.total <= state.dataList!.length) {
        state.isNoMoreDataState = true;
      } else {
        state.isNoMoreDataState = false;
      }

      if (state.pageNo == 1) {
        state.dataList = result.records;
      } else {
        state.dataList = List.from(state.dataList!)..addAll(result.records);
      }
      state.netState = NetState.dataSuccessState;
      if (state.dataList!.isEmpty) state.netState = NetState.emptyDataState;
      emit(state.clone());
    }
  }
}
