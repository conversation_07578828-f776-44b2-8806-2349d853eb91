PODS:
  - AliPlayerSDK_iOS (6.21.0):
    - AliPlayerSDK_iOS/AliPlayerSDK (= 6.21.0)
  - AliPlayerSDK_iOS/AliPlayerSDK (6.21.0)
  - app_badge_plus (1.2.3):
    - Flutter
  - AppAuth (1.7.6):
    - AppAuth/Core (= 1.7.6)
    - AppAuth/ExternalUserAgent (= 1.7.6)
  - AppAuth/Core (1.7.6)
  - AppAuth/ExternalUserAgent (1.7.6):
    - AppAuth/Core
  - AppCheckCore (11.2.0):
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - audio_session (0.0.1):
    - Flutter
  - camera_avfoundation (0.0.1):
    - Flutter
  - captcha_plugin_flutter (0.0.2):
    - Flutter
    - NTESVerifyCode (= 3.6.7)
  - connectivity_plus (0.0.1):
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - DKImagePickerController/Core (4.3.9):
    - DKImagePickerController/ImageDataManager
    - DKImagePickerController/Resource
  - DKImagePickerController/ImageDataManager (4.3.9)
  - DKImagePickerController/PhotoGallery (4.3.9):
    - DKImagePickerController/Core
    - DKPhotoGallery
  - DKImagePickerController/Resource (4.3.9)
  - DKPhotoGallery (0.0.19):
    - DKPhotoGallery/Core (= 0.0.19)
    - DKPhotoGallery/Model (= 0.0.19)
    - DKPhotoGallery/Preview (= 0.0.19)
    - DKPhotoGallery/Resource (= 0.0.19)
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Core (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Preview
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Model (0.0.19):
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Preview (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Resource
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Resource (0.0.19):
    - SDWebImage
    - SwiftyGif
  - fc_native_video_thumbnail (0.0.1):
    - Flutter
    - FlutterMacOS
  - file_picker (0.0.1):
    - DKImagePickerController/PhotoGallery
    - Flutter
  - Firebase/Auth (11.13.0):
    - Firebase/CoreOnly
    - FirebaseAuth (~> 11.13.0)
  - Firebase/CoreOnly (11.13.0):
    - FirebaseCore (~> 11.13.0)
  - Firebase/Crashlytics (11.13.0):
    - Firebase/CoreOnly
    - FirebaseCrashlytics (~> 11.13.0)
  - firebase_auth (5.6.0):
    - Firebase/Auth (= 11.13.0)
    - firebase_core
    - Flutter
  - firebase_core (3.14.0):
    - Firebase/CoreOnly (= 11.13.0)
    - Flutter
  - firebase_crashlytics (4.3.7):
    - Firebase/Crashlytics (= 11.13.0)
    - firebase_core
    - Flutter
  - FirebaseAppCheckInterop (11.15.0)
  - FirebaseAuth (11.13.0):
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseAuthInterop (~> 11.0)
    - FirebaseCore (~> 11.13.0)
    - FirebaseCoreExtension (~> 11.13.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/Environment (~> 8.1)
    - GTMSessionFetcher/Core (< 5.0, >= 3.4)
    - RecaptchaInterop (~> 101.0)
  - FirebaseAuthInterop (11.15.0)
  - FirebaseCore (11.13.0):
    - FirebaseCoreInternal (~> 11.13.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/Logger (~> 8.1)
  - FirebaseCoreExtension (11.13.0):
    - FirebaseCore (~> 11.13.0)
  - FirebaseCoreInternal (11.13.0):
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
  - FirebaseCrashlytics (11.13.0):
    - FirebaseCore (~> 11.13.0)
    - FirebaseInstallations (~> 11.0)
    - FirebaseRemoteConfigInterop (~> 11.0)
    - FirebaseSessions (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.1)
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - FirebaseInstallations (11.13.0):
    - FirebaseCore (~> 11.13.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - PromisesObjC (~> 2.4)
  - FirebaseRemoteConfigInterop (11.15.0)
  - FirebaseSessions (11.13.0):
    - FirebaseCore (~> 11.13.0)
    - FirebaseCoreExtension (~> 11.13.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - nanopb (~> 3.30910.0)
    - PromisesSwift (~> 2.1)
  - Flutter (1.0.0)
  - flutter_aliplayer (6.21.0):
    - Flutter
    - flutter_aliplayer/AliPlayerSDKFrameworks (= 6.21.0)
    - MJExtension
  - flutter_aliplayer/AliPlayerSDKFrameworks (6.21.0):
    - AliPlayerSDK_iOS (= 6.21.0)
    - Flutter
    - MJExtension
  - flutter_avif_ios (0.0.1):
    - Flutter
  - flutter_image_compress_common (1.0.0):
    - Flutter
    - Mantle
    - SDWebImage
    - SDWebImageWebPCoder
  - flutter_image_gallery_saver (0.0.1):
    - Flutter
  - flutter_keyboard_visibility (0.0.1):
    - Flutter
  - flutter_native_splash (2.4.3):
    - Flutter
  - flutter_plugin_engagelab (0.0.3):
    - Flutter
    - MTPush (= 4.4.0)
  - fluttertoast (0.0.2):
    - Flutter
  - google_sign_in_ios (0.0.1):
    - AppAuth (>= 1.7.4)
    - Flutter
    - FlutterMacOS
    - GoogleSignIn (~> 8.0)
    - GTMSessionFetcher (>= 3.4.0)
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleSignIn (8.0.0):
    - AppAuth (< 2.0, >= 1.7.3)
    - AppCheckCore (~> 11.0)
    - GTMAppAuth (< 5.0, >= 4.1.1)
    - GTMSessionFetcher/Core (~> 3.3)
  - GoogleUtilities/AppDelegateSwizzler (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.1.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/Reachability (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMAppAuth (4.1.1):
    - AppAuth/Core (~> 1.7)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3)
  - GTMSessionFetcher (3.5.0):
    - GTMSessionFetcher/Full (= 3.5.0)
  - GTMSessionFetcher/Core (3.5.0)
  - GTMSessionFetcher/Full (3.5.0):
    - GTMSessionFetcher/Core
  - HydraAsync (2.0.6)
  - image_gallery_saver (2.0.2):
    - Flutter
  - image_gallery_saver_plus (0.0.1):
    - Flutter
  - image_picker_ios (0.0.1):
    - Flutter
  - just_audio (0.0.1):
    - Flutter
    - FlutterMacOS
  - libOpenInstallSDK (2.8.6)
  - libwebp (1.5.0):
    - libwebp/demux (= 1.5.0)
    - libwebp/mux (= 1.5.0)
    - libwebp/sharpyuv (= 1.5.0)
    - libwebp/webp (= 1.5.0)
  - libwebp/demux (1.5.0):
    - libwebp/webp
  - libwebp/mux (1.5.0):
    - libwebp/demux
  - libwebp/sharpyuv (1.5.0)
  - libwebp/webp (1.5.0):
    - libwebp/sharpyuv
  - Mantle (2.2.0):
    - Mantle/extobjc (= 2.2.0)
  - Mantle/extobjc (2.2.0)
  - media_kit_libs_ios_video (1.0.4):
    - Flutter
  - media_kit_video (0.0.1):
    - Flutter
  - memory_info (0.0.1):
    - Flutter
  - MJExtension (3.4.2)
  - MTPush (4.4.0)
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - NTESVerifyCode (3.6.7)
  - open_file_ios (0.0.1):
    - Flutter
  - openinstall_flutter_plugin (0.0.1):
    - Flutter
    - libOpenInstallSDK
  - package_info_plus (0.4.5):
    - Flutter
  - pasteboard (0.0.1):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - photo_manager (3.7.1):
    - Flutter
    - FlutterMacOS
  - pointer_interceptor_ios (0.0.1):
    - Flutter
  - PromisesObjC (2.4.0)
  - PromisesSwift (2.4.0):
    - PromisesObjC (= 2.4.0)
  - RecaptchaInterop (101.0.0)
  - record_darwin (1.0.0):
    - Flutter
  - SDWebImage (5.21.1):
    - SDWebImage/Core (= 5.21.1)
  - SDWebImage/Core (5.21.1)
  - SDWebImageWebPCoder (0.14.6):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.17)
  - sensors_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - SwiftyGif (5.4.5)
  - tencent_cloud_chat_sdk (8.0.0):
    - Flutter
    - HydraAsync
    - TXIMSDK_Plus_iOS_XCFramework (= 8.5.6864)
  - tencent_cloud_uikit_core (0.0.1):
    - Flutter
  - TXIMSDK_Plus_iOS_XCFramework (8.5.6864)
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - volume_controller (0.0.1):
    - Flutter
  - wakelock_plus (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - app_badge_plus (from `.symlinks/plugins/app_badge_plus/ios`)
  - audio_session (from `.symlinks/plugins/audio_session/ios`)
  - camera_avfoundation (from `.symlinks/plugins/camera_avfoundation/ios`)
  - captcha_plugin_flutter (from `.symlinks/plugins/captcha_plugin_flutter/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - fc_native_video_thumbnail (from `.symlinks/plugins/fc_native_video_thumbnail/darwin`)
  - file_picker (from `.symlinks/plugins/file_picker/ios`)
  - firebase_auth (from `.symlinks/plugins/firebase_auth/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_crashlytics (from `.symlinks/plugins/firebase_crashlytics/ios`)
  - Flutter (from `Flutter`)
  - flutter_aliplayer (from `.symlinks/plugins/flutter_aliplayer/ios`)
  - flutter_avif_ios (from `.symlinks/plugins/flutter_avif_ios/ios`)
  - flutter_image_compress_common (from `.symlinks/plugins/flutter_image_compress_common/ios`)
  - flutter_image_gallery_saver (from `.symlinks/plugins/flutter_image_gallery_saver/ios`)
  - flutter_keyboard_visibility (from `.symlinks/plugins/flutter_keyboard_visibility/ios`)
  - flutter_native_splash (from `.symlinks/plugins/flutter_native_splash/ios`)
  - flutter_plugin_engagelab (from `.symlinks/plugins/flutter_plugin_engagelab/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - google_sign_in_ios (from `.symlinks/plugins/google_sign_in_ios/darwin`)
  - image_gallery_saver (from `.symlinks/plugins/image_gallery_saver/ios`)
  - image_gallery_saver_plus (from `.symlinks/plugins/image_gallery_saver_plus/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - just_audio (from `.symlinks/plugins/just_audio/darwin`)
  - media_kit_libs_ios_video (from `.symlinks/plugins/media_kit_libs_ios_video/ios`)
  - media_kit_video (from `.symlinks/plugins/media_kit_video/ios`)
  - memory_info (from `.symlinks/plugins/memory_info/ios`)
  - open_file_ios (from `.symlinks/plugins/open_file_ios/ios`)
  - openinstall_flutter_plugin (from `.symlinks/plugins/openinstall_flutter_plugin/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - pasteboard (from `.symlinks/plugins/pasteboard/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - photo_manager (from `.symlinks/plugins/photo_manager/ios`)
  - pointer_interceptor_ios (from `.symlinks/plugins/pointer_interceptor_ios/ios`)
  - record_darwin (from `.symlinks/plugins/record_darwin/ios`)
  - sensors_plus (from `.symlinks/plugins/sensors_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - tencent_cloud_chat_sdk (from `.symlinks/plugins/tencent_cloud_chat_sdk/ios`)
  - tencent_cloud_uikit_core (from `.symlinks/plugins/tencent_cloud_uikit_core/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)
  - volume_controller (from `.symlinks/plugins/volume_controller/ios`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)

SPEC REPOS:
  trunk:
    - AliPlayerSDK_iOS
    - AppAuth
    - AppCheckCore
    - DKImagePickerController
    - DKPhotoGallery
    - Firebase
    - FirebaseAppCheckInterop
    - FirebaseAuth
    - FirebaseAuthInterop
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseInstallations
    - FirebaseRemoteConfigInterop
    - FirebaseSessions
    - GoogleDataTransport
    - GoogleSignIn
    - GoogleUtilities
    - GTMAppAuth
    - GTMSessionFetcher
    - HydraAsync
    - libOpenInstallSDK
    - libwebp
    - Mantle
    - MJExtension
    - MTPush
    - nanopb
    - NTESVerifyCode
    - PromisesObjC
    - PromisesSwift
    - RecaptchaInterop
    - SDWebImage
    - SDWebImageWebPCoder
    - SwiftyGif
    - TXIMSDK_Plus_iOS_XCFramework

EXTERNAL SOURCES:
  app_badge_plus:
    :path: ".symlinks/plugins/app_badge_plus/ios"
  audio_session:
    :path: ".symlinks/plugins/audio_session/ios"
  camera_avfoundation:
    :path: ".symlinks/plugins/camera_avfoundation/ios"
  captcha_plugin_flutter:
    :path: ".symlinks/plugins/captcha_plugin_flutter/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  fc_native_video_thumbnail:
    :path: ".symlinks/plugins/fc_native_video_thumbnail/darwin"
  file_picker:
    :path: ".symlinks/plugins/file_picker/ios"
  firebase_auth:
    :path: ".symlinks/plugins/firebase_auth/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_crashlytics:
    :path: ".symlinks/plugins/firebase_crashlytics/ios"
  Flutter:
    :path: Flutter
  flutter_aliplayer:
    :path: ".symlinks/plugins/flutter_aliplayer/ios"
  flutter_avif_ios:
    :path: ".symlinks/plugins/flutter_avif_ios/ios"
  flutter_image_compress_common:
    :path: ".symlinks/plugins/flutter_image_compress_common/ios"
  flutter_image_gallery_saver:
    :path: ".symlinks/plugins/flutter_image_gallery_saver/ios"
  flutter_keyboard_visibility:
    :path: ".symlinks/plugins/flutter_keyboard_visibility/ios"
  flutter_native_splash:
    :path: ".symlinks/plugins/flutter_native_splash/ios"
  flutter_plugin_engagelab:
    :path: ".symlinks/plugins/flutter_plugin_engagelab/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  google_sign_in_ios:
    :path: ".symlinks/plugins/google_sign_in_ios/darwin"
  image_gallery_saver:
    :path: ".symlinks/plugins/image_gallery_saver/ios"
  image_gallery_saver_plus:
    :path: ".symlinks/plugins/image_gallery_saver_plus/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  just_audio:
    :path: ".symlinks/plugins/just_audio/darwin"
  media_kit_libs_ios_video:
    :path: ".symlinks/plugins/media_kit_libs_ios_video/ios"
  media_kit_video:
    :path: ".symlinks/plugins/media_kit_video/ios"
  memory_info:
    :path: ".symlinks/plugins/memory_info/ios"
  open_file_ios:
    :path: ".symlinks/plugins/open_file_ios/ios"
  openinstall_flutter_plugin:
    :path: ".symlinks/plugins/openinstall_flutter_plugin/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  pasteboard:
    :path: ".symlinks/plugins/pasteboard/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  photo_manager:
    :path: ".symlinks/plugins/photo_manager/ios"
  pointer_interceptor_ios:
    :path: ".symlinks/plugins/pointer_interceptor_ios/ios"
  record_darwin:
    :path: ".symlinks/plugins/record_darwin/ios"
  sensors_plus:
    :path: ".symlinks/plugins/sensors_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  tencent_cloud_chat_sdk:
    :path: ".symlinks/plugins/tencent_cloud_chat_sdk/ios"
  tencent_cloud_uikit_core:
    :path: ".symlinks/plugins/tencent_cloud_uikit_core/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"
  volume_controller:
    :path: ".symlinks/plugins/volume_controller/ios"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"

SPEC CHECKSUMS:
  AliPlayerSDK_iOS: 9598c53c095bb4aca8b956f698f4d9f4ceeadeec
  app_badge_plus: 73079d53b6a318c723de4d8aded465fbc4e92897
  AppAuth: d4f13a8fe0baf391b2108511793e4b479691fb73
  AppCheckCore: cc8fd0a3a230ddd401f326489c99990b013f0c4f
  audio_session: 19e9480dbdd4e5f6c4543826b2e8b0e4ab6145fe
  camera_avfoundation: adb0207d868b2d873e895371d88448399ab78d87
  captcha_plugin_flutter: 67dc0d2e77ab5cf0b189b133123bb4f10c191d35
  connectivity_plus: 2a701ffec2c0ae28a48cf7540e279787e77c447d
  device_info_plus: 97af1d7e84681a90d0693e63169a5d50e0839a0d
  DKImagePickerController: 946cec48c7873164274ecc4624d19e3da4c1ef3c
  DKPhotoGallery: b3834fecb755ee09a593d7c9e389d8b5d6deed60
  fc_native_video_thumbnail: 927d4dcfd4c7e9f2cc1a20bb52dfee83de3792c2
  file_picker: 09aa5ec1ab24135ccd7a1621c46c84134bfd6655
  Firebase: 3435bc66b4d494c2f22c79fd3aae4c1db6662327
  firebase_auth: a9855fe79286576c5225b28aafed65bc374ac19a
  firebase_core: a861be150c0e7c6aecedde077968eb92cbf790b9
  firebase_crashlytics: aa26a226ac9a7047f729701fd8a60028a21f84ac
  FirebaseAppCheckInterop: 06fe5a3799278ae4667e6c432edd86b1030fa3df
  FirebaseAuth: 175cb5503dfdb52191b8ff81cdd52c1d9dee9ac9
  FirebaseAuthInterop: 7087d7a4ee4bc4de019b2d0c240974ed5d89e2fd
  FirebaseCore: c692c7f1c75305ab6aff2b367f25e11d73aa8bd0
  FirebaseCoreExtension: c048485c347616dba6165358dbef765c5197597b
  FirebaseCoreInternal: 29d7b3af4aaf0b8f3ed20b568c13df399b06f68c
  FirebaseCrashlytics: 8281e577b6f85a08ea7aeb8b66f95e1ae430c943
  FirebaseInstallations: 0ee9074f2c1e86561ace168ee1470dc67aabaf02
  FirebaseRemoteConfigInterop: 1c6135e8a094cc6368949f5faeeca7ee8948b8aa
  FirebaseSessions: eaa8ec037e7793769defe4201c20bd4d976f9677
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_aliplayer: 69ea6db5774fb057f2a20a7c874081c2ba7909d9
  flutter_avif_ios: 1db95ee8dba72351a589e4945b94bfbca8d382d1
  flutter_image_compress_common: ec1d45c362c9d30a3f6a0426c297f47c52007e3e
  flutter_image_gallery_saver: f356b5f265ba4e36f38b2fe96d0468e0712a6b37
  flutter_keyboard_visibility: 0339d06371254c3eb25eeb90ba8d17dca8f9c069
  flutter_native_splash: f71420956eb811e6d310720fee915f1d42852e7a
  flutter_plugin_engagelab: 4adc041401daea227a6c016fd4098b339e615b32
  fluttertoast: 21eecd6935e7064cc1fcb733a4c5a428f3f24f0f
  google_sign_in_ios: 7411fab6948df90490dc4620ecbcabdc3ca04017
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleSignIn: ce8c89bb9b37fb624b92e7514cc67335d1e277e4
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  GTMAppAuth: f69bd07d68cd3b766125f7e072c45d7340dea0de
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  HydraAsync: 8d589bd725b0224f899afafc9a396327405f8063
  image_gallery_saver: cb43cc43141711190510e92c460eb1655cd343cb
  image_gallery_saver_plus: 782ade975fe6a4600b53e7c1983e3a2979d1e9e5
  image_picker_ios: c560581cceedb403a6ff17f2f816d7fea1421fc1
  just_audio: a42c63806f16995daf5b219ae1d679deb76e6a79
  libOpenInstallSDK: 4473c8a86c6f2a31147cbcbc63ab5fa9479c78ce
  libwebp: 02b23773aedb6ff1fd38cec7a77b81414c6842a8
  Mantle: c5aa8794a29a022dfbbfc9799af95f477a69b62d
  media_kit_libs_ios_video: a5fe24bc7875ccd6378a0978c13185e1344651c1
  media_kit_video: 5da63f157170e5bf303bf85453b7ef6971218a2e
  memory_info: 18a37c81d0c57151f6661fd007b05f8894427a0a
  MJExtension: e97d164cb411aa9795cf576093a1fa208b4a8dd8
  MTPush: 88322fc0a046d1bbc9babc7fb7c032d276107e63
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  NTESVerifyCode: 3b4dc542427905f7c876f293076012a5ae9735bc
  open_file_ios: 461db5853723763573e140de3193656f91990d9e
  openinstall_flutter_plugin: e6b8486f834eb60b336546442a8b747d4b664cf4
  package_info_plus: c0502532a26c7662a62a356cebe2692ec5fe4ec4
  pasteboard: 982969ebaa7c78af3e6cc7761e8f5e77565d9ce0
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  permission_handler_apple: 9878588469a2b0d0fc1e048d9f43605f92e6cec2
  photo_manager: 81954a1bf804b6e882d0453b3b6bc7fad7b47d3d
  pointer_interceptor_ios: 508241697ff0947f853c061945a8b822463947c1
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PromisesSwift: 9d77319bbe72ebf6d872900551f7eeba9bce2851
  RecaptchaInterop: 11e0b637842dfb48308d242afc3f448062325aba
  record_darwin: 3b1a8e7d5c0cbf45ad6165b4d83a6ca643d929c3
  SDWebImage: f29024626962457f3470184232766516dee8dfea
  SDWebImageWebPCoder: e38c0a70396191361d60c092933e22c20d5b1380
  sensors_plus: 7229095999f30740798f0eeef5cd120357a8f4f2
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  sqflite_darwin: 5a7236e3b501866c1c9befc6771dfd73ffb8702d
  SwiftyGif: 706c60cf65fa2bc5ee0313beece843c8eb8194d4
  tencent_cloud_chat_sdk: 1eff6c33bfed6be05ee23b32635537fc33728dd1
  tencent_cloud_uikit_core: c0140985a33df7b3948d2ebe0a0d7eeb780447f2
  TXIMSDK_Plus_iOS_XCFramework: 0353712b504d2206ce0f4d94b0eb3357673e1cfe
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe
  video_player_avfoundation: 7c6c11d8470e1675df7397027218274b6d2360b3
  volume_controller: 2e3de73d6e7e81a0067310d17fb70f2f86d71ac7
  wakelock_plus: 76957ab028e12bfa4e66813c99e46637f367fc7e
  webview_flutter_wkwebview: 0982481e3d9c78fd5c6f62a002fcd24fc791f1e4

PODFILE CHECKSUM: 04d3219d49efc84a321b23e610e50ffd81fe3dea

COCOAPODS: 1.16.2
