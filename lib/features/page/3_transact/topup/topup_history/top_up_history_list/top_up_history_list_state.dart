import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/constants/enums.dart';
import 'package:wd/shared/widgets/transact/transact_history_filter_popup.dart';

class TopUpHistoryListState extends BaseState {
  List<TransactFilterItem> filterWayList = [];
  List<TransactFilterItem> filterTypeList = [];
  RecordDateType currentDateType = RecordDateType.today;
  String userNo = "";

  int pageNo = 1;

  TopUpHistoryListState init() {
    return TopUpHistoryListState()
      ..pageNo = 1
      ..filterWayList = []
      ..filterTypeList = []
      ..currentDateType = RecordDateType.today
      ..userNo = ""
      ..isNoMoreDataState = false
      ..dataList = [];
  }

  TopUpHistoryListState clone() {
    return TopUpHistoryListState()
      ..pageNo = pageNo
      ..filterWayList = filterWayList
      ..filterTypeList = filterTypeList
      ..currentDateType = currentDateType
      ..userNo = userNo
      ..dataList = dataList
      ..isNoMoreDataState = isNoMoreDataState
      ..netState = netState;
  }
}
