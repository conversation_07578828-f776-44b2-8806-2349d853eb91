import 'package:flutter/material.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

enum CommonButtonStyle {primary, secondary, tertiary, quaternary}

class CommonButton extends StatefulWidget {
  final Widget? child;
  final String title;
  final EdgeInsets? titlePadding;
  final double? width;
  final double? height;
  final String? bgImgPath;
  final CommonButtonStyle style;
  final Color? textColor;
  final Color? backgroundColor;
  final double fontSize;
  final FontWeight? fontWeight;
  final Color? borderColor;
  final VoidCallback? onPressed;
  final double? radius;
  final bool enable;
  final bool showLoading;
  final int delayMilliseconds;
  final Widget? prefix;
  final double prefixLeftPadding;

  CommonButton({
    super.key,
    required this.title,
    this.titlePadding,
    this.width,
    this.height,
    this.bgImgPath,
    this.child,
    this.style = CommonButtonStyle.primary,
    this.textColor,
    this.backgroundColor,
    double? fontSize,
    this.fontWeight,
    this.borderColor,
    this.radius,
    this.onPressed,
    this.showLoading = false,
    this.enable = true,
    this.delayMilliseconds = 2000, // 默认延迟2秒
    this.prefix,
    double? prefixLeftPadding,
  }) : prefixLeftPadding = prefixLeftPadding ?? 15.gw, fontSize = fontSize ?? 16.gw;

  @override
  State createState() => _CommonButtonState();
}

class _CommonButtonState extends State<CommonButton> {
  bool _isClickable = true;

  Color bgColor = Colors.white;
  Color loadingBgColor = Colors.white;
  Color borderColor = Colors.white;
  Color textColor = Colors.blue;

  void _handleOnPressed() async {
    if (_isClickable && widget.onPressed != null) {
      widget.onPressed!();
      if (widget.delayMilliseconds > 0) {
        setState(() {
          _isClickable = false;
        });
        await Future.delayed(Duration(milliseconds: widget.delayMilliseconds));
        if (mounted) {
          setState(() {
            _isClickable = true;
          });
        }
      }
    }
  }

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        CustomColorTheme colorTheme = context.colorTheme;

        setState(() {
          switch (widget.style) {
            case CommonButtonStyle.primary:
              bgColor = colorTheme.btnBgPrimary;
              textColor = colorTheme.btnTitlePrimary;
              loadingBgColor = colorTheme.btnTitlePrimary;
              borderColor = colorTheme.btnBorderPrimary;
              break;
            case CommonButtonStyle.secondary:
              bgColor = colorTheme.btnBgSecondary;
              textColor = colorTheme.btnTitleSecondary;
              loadingBgColor = colorTheme.btnTitleSecondary;
              borderColor = colorTheme.btnBorderSecondary;
              break;
            case CommonButtonStyle.tertiary:
              bgColor = colorTheme.btnBgTertiary;
              textColor = colorTheme.btnTitleTertiary;
              loadingBgColor = colorTheme.btnTitleTertiary;
              borderColor = colorTheme.btnBorderTertiary;
              break;
            case CommonButtonStyle.quaternary:
              bgColor = colorTheme.tabItemBgA;
              textColor = context.theme.primaryColor;
              loadingBgColor = colorTheme.btnTitleTertiary;
              borderColor = colorTheme.borderE;
              break;
          }
        });
      }
    });

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return ConstrainedBox(
      constraints: BoxConstraints.tightFor(width: widget.width, height: widget.height),
      child: ElevatedButton(
          onPressed: widget.enable ? (widget.showLoading ? () {} : _handleOnPressed) : null,
          style: ElevatedButton.styleFrom(
            splashFactory: NoSplash.splashFactory,
            shadowColor: Colors.transparent,
            padding: EdgeInsets.zero,
            disabledBackgroundColor: widget.bgImgPath != null ? Colors.transparent: Theme.of(context).disabledColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(widget.radius ?? 8.gw),
            ),
            side:
                BorderSide(width: 1, color: widget.borderColor ?? borderColor),
            backgroundColor: widget.backgroundColor ?? (widget.showLoading ? loadingBgColor : bgColor),
            fixedSize: Size(widget.width ?? double.infinity, widget.height ?? 50.gw),

            minimumSize: Size(widget.width ?? double.infinity, widget.height ?? 50.gw),
            maximumSize: Size(widget.width ?? double.infinity, widget.height ?? 50.gw),
            elevation: 0,
          ),
          child: widget.child ??
              Ink(
                decoration: widget.bgImgPath != null
                    ? BoxDecoration(
                        image: DecorationImage(
                          image: AssetImage(widget.bgImgPath!), // 背景图片
                          fit: BoxFit.cover, // 调整图片的适应方式
                        ),
                        borderRadius: BorderRadius.circular(widget.radius ?? 25.gw), // 图片圆角与按钮圆角匹配
                      )
                    : null,
                child: Container(
                  constraints: const BoxConstraints.expand(),
                  padding: widget.titlePadding,
                  alignment: Alignment.center, // 文字居中
                  child: widget.showLoading
                      ? SizedBox(
                          width: 15.gw,
                          height: 15.gw,
                          child: RepaintBoundary(
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: textColor,
                            ),
                          ),
                        )
                      : Stack(
                          children: [
                            if (widget.prefix != null) ...[
                              Positioned(
                                left: 15.gw,
                                top: 0,
                                bottom: 0,
                                child: Center(child: widget.prefix),
                              ),
                            ],
                            Center(
                              child: AneText(
                                widget.title,
                                style: TextStyle(
                                  fontSize: widget.fontSize,
                                  fontWeight: widget.fontWeight ?? FontWeight.w600,
                                  color: widget.textColor ?? textColor,
                                ),
                              ),
                            ),
                          ],
                        ),
                ),
              )),
    );
  }
}
