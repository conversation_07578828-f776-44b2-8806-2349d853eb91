import 'package:bloc/bloc.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/constants/enums.dart';
import 'package:wd/core/models/apis/statement.dart';
import 'package:wd/core/utils/time_util.dart';
import 'package:wd/shared/widgets/easy_loading.dart';
import 'package:wd/shared/widgets/statement/statement_cell.dart';
import 'package:wd/shared/widgets/transact/transact_history_filter_popup.dart';

import 'statement_state.dart';

class StatementCubit extends Cubit<StatementState> {
  StatementCubit() : super(StatementState().init()) {
    fetchListFilterWayList();
    fetchListFilterTypeList();
    fetchListData();
  }

  String lastWayCode = "";
  String lastTypeCode = "";

  void currentDateTypeChanged(RecordDateType type) {
    if (state.currentDateType != type) {
      state.currentDateType = type;
      state.pageNo = 1;
      fetchListData();
    }
  }

  void updatePageNo(int pageNo) {
    state.pageNo = pageNo;
    emit(state.clone());
  }

  void updatePageNoToNext() {
    state.pageNo += 1;
    emit(state.clone());
  }

  void updateIsNoMoreDataState(bool isNoMoreDataState) {
    state.isNoMoreDataState = isNoMoreDataState;
    emit(state.clone());
  }

  fetchListFilterWayList() async {
    var list = await StatementApi.fetchStatementFilterWay();
    var tmpList = list.map((e) => TransactFilterItem.fromStatementFilterWay(e)).toList();
    if (tmpList.isNotEmpty) {
      var allType = TransactFilterItem(name: "全部", code: "", isSel: true);
      state.filterWayList = List.from(tmpList)..insert(0, allType);
      emit(state.clone());
    }
  }

  fetchListFilterTypeList() async {
    var list = await StatementApi.fetchStatementFilterType();
    var tmpList = list.map((e) => TransactFilterItem.fromStatementFilterType(e)).toList();
    if (tmpList.isNotEmpty) {
      var allType = TransactFilterItem(name: "全部", code: "", isSel: true);
      state.filterTypeList = List.from(tmpList)..insert(0, allType);
      emit(state.clone());
    }
  }

  fetchListDataWithFilter() {
    final wayCode = state.filterWayList.where((e) => e.isSel).map((e) => e.code).toList().join(",");
    final typeCode = state.filterTypeList.where((e) => e.isSel).map((e) => e.code).toList().join(",");
    if (wayCode != lastWayCode || typeCode != lastTypeCode) {
      lastWayCode = wayCode;
      lastTypeCode = typeCode;
      fetchListData();
    }
  }

  fetchListData() async {
    GSEasyLoading.showLoading();
    final dateRange = TimeUtil.getDateRange(state.currentDateType);
    final wayCode = state.filterWayList.where((e) => e.isSel).map((e) => e.code).toList().join(",");
    final typeCode = state.filterTypeList.where((e) => e.isSel).map((e) => e.code).toList().join(",");

    final result = await StatementApi.fetchStatement(
      pageNo: state.pageNo,
      startDate: dateRange.$1,
      endDate: dateRange.$2,
      wayCode: wayCode,
      typeCode: typeCode,
    );

    GSEasyLoading.dismiss();

    if (result == null) {
      state.netState = NetState.error404State;
      state.dataList = [];
    } else {
      if (state.pageNo == 1) {
        state.dataList = result.records.map((e) => GSStateListCellModel.formStatementRecords(e)).toList();
      } else {
        state.dataList = List.from(state.dataList!)
          ..addAll(result.records.map((e) => GSStateListCellModel.formStatementRecords(e)).toList());
      }
      state.isNoMoreDataState = result.total <= state.dataList!.length;
      state.netState = state.dataList!.isNotEmpty ? NetState.dataSuccessState : NetState.emptyDataState;
    }

    emit(state.clone());
  }
}
